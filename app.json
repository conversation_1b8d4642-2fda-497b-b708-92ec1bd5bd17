{"expo": {"name": "Free Plant Identifier Social App", "slug": "plantsconnect-plant-identifier-social-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "plantconnects", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.plantconnects.app", "infoPlist": {"NSCameraUsageDescription": "Allow $(PRODUCT_NAME) to access your camera", "NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to access your microphone", "NSPhotoLibraryUsageDescription": "Allow $(PRODUCT_NAME) to access your photos"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.plantconnects.app", "permissions": ["android.permission.VIBRATE", "CAMERA", "RECORD_AUDIO", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "INTERNET"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://plantconnects.com/"}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}]], "experiments": {"typedRoutes": true}, "extra": {"supabaseUrl": "https://zlivouxymzpbyoxnwxrp.supabase.co", "supabaseAnonKey": "sb_publishable_ABgFmonyDTQFCL9IiD9Pzw_H7-dr7yC", "openrouterApiKey": "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"}}}